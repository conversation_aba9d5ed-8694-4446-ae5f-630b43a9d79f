import React, { useState, useEffect, useMemo } from 'react';
import api from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription, DialogTrigger, DialogClose } from '@/components/ui/dialog';
import Swal from 'sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';
import '../styles/user-management.css';
import { cn } from '@/lib/utils';
import UserDetailsModal from './UserDetailsModal';
import UserEditModal from './UserEditModal';
import { User, Users, Search, PlusCircle, Trash2, Eye, AlertCircle, RefreshCw, X, Loader2, FilePenLine, UserCheck, Shield, HardHat, UserX, Mail, Phone, Briefcase, CheckCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface IUser {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  role: string;
  statut: string;
  permissions?: string[];
  date_creation?: string;
  derniere_connexion?: string;
}

export function UserManagement() {
  const [users, setUsers] = useState<IUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [savingUser, setSavingUser] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<IUser | null>(null);
  const [newUser, setNewUser] = useState({
    nom: '',
    email: '',
    telephone: '',
    role: 'Opérateur',
    statut: 'Actif',
  });

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await api.get('/users.php');
      if (response.data.success) {
        setUsers(response.data.data || []);
      } else {
        throw new Error(response.data.message || 'Erreur lors de la récupération des utilisateurs.');
      }
    } catch (error: any) {
      console.error("Erreur détaillée lors de la récupération des utilisateurs:", error);
      setError(`Impossible de charger les utilisateurs. Détails: ${error.message}`);
      Swal.fire({
        icon: 'error',
        title: 'Erreur de Connexion',
        text: `La connexion au serveur a échoué. Veuillez vérifier votre connexion ou contacter un administrateur. Détails: ${error.message}`,
        confirmButtonColor: '#d33',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleAddUser = async () => {
    if (!newUser.nom || !newUser.email) {
      Swal.fire('Champs requis', 'Le nom et l\'email sont obligatoires.', 'warning');
      return;
    }
    setSavingUser(true);
    try {
      const response = await api.post('/users.php', { action: 'create', ...newUser });
      if (response.data.success) {
        Swal.fire('Succès', 'Utilisateur ajouté avec succès!', 'success');
        setIsAddModalOpen(false);
        setNewUser({ nom: '', email: '', telephone: '', role: 'Opérateur', statut: 'Actif' });
        fetchUsers();
      } else {
        throw new Error(response.data.message || 'Une erreur est survenue lors de l\'ajout.');
      }
    } catch (error: any) {
      Swal.fire('Erreur', error.message, 'error');
    } finally {
      setSavingUser(false);
    }
  };

  const handleSaveUser = async (userToSave: IUser) => {
    setSavingUser(true);
    try {
      const response = await api.put(`/users.php?id=${userToSave.id}`, { action: 'update', ...userToSave });
      if (response.data.success) {
        Swal.fire('Succès', 'Utilisateur mis à jour avec succès!', 'success');
        setIsEditModalOpen(false);
        fetchUsers();
      } else {
        throw new Error(response.data.message || 'Une erreur est survenue lors de la mise à jour.');
      }
    } catch (error: any) {
      Swal.fire('Erreur', error.message, 'error');
    } finally {
      setSavingUser(false);
    }
  };

  const handleToggleUserStatus = async (user: IUser) => {
    const newStatus = user.statut === 'Actif' ? 'Inactif' : 'Actif';
    const result = await Swal.fire({
      title: `Confirmer le changement de statut`,
      text: `Voulez-vous vraiment passer le statut de ${user.nom} à "${newStatus}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Oui, changer!',
      cancelButtonText: 'Annuler',
    });

    if (result.isConfirmed) {
      try {
        const response = await api.put(`/users.php?id=${user.id}`, { action: 'update_status', statut: newStatus });
        if (response.data.success) {
          Swal.fire('Statut modifié!', `Le statut de ${user.nom} a été mis à jour.`, 'success');
          fetchUsers();
        } else {
          throw new Error(response.data.message || 'Erreur lors du changement de statut.');
        }
      } catch (error: any) {
        Swal.fire('Erreur', error.message, 'error');
      }
    }
  };

  const handleDeleteUser = async (userId: string) => {
    const userToDelete = users.find(u => u.id === userId);
    if (!userToDelete) return;

    const result = await Swal.fire({
      title: 'Êtes-vous sûr?',
      text: `Vous êtes sur le point de supprimer l'utilisateur "${userToDelete.nom}". Cette action est irréversible!`,
      icon: 'error',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Oui, supprimer!',
      cancelButtonText: 'Annuler',
    });

    if (result.isConfirmed) {
      try {
        const response = await api.delete(`/users.php?id=${userId}`);
        if (response.data.success) {
          Swal.fire('Supprimé!', 'L\'utilisateur a été supprimé.', 'success');
          fetchUsers();
        } else {
          throw new Error(response.data.message || 'Erreur lors de la suppression.');
        }
      } catch (error: any) {
        Swal.fire('Erreur', error.message, 'error');
      }
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'Administrateur':
        return <Badge className="role-badge-admin px-3 py-1 text-xs font-semibold rounded-full border-0">{role}</Badge>;
      case 'Superviseur':
        return <Badge className="role-badge-supervisor px-3 py-1 text-xs font-semibold rounded-full border-0">{role}</Badge>;
      case 'Opérateur':
        return <Badge className="role-badge-operator px-3 py-1 text-xs font-semibold rounded-full border-0">{role}</Badge>;
      default:
        return <Badge className="bg-gray-500 text-white px-3 py-1 text-xs font-semibold rounded-full border-0">{role}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Actif':
        return <Badge className="status-badge-active px-3 py-1 text-xs font-semibold rounded-full border-0 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          {status}
        </Badge>;
      case 'Inactif':
        return <Badge className="status-badge-inactive px-3 py-1 text-xs font-semibold rounded-full border-0 flex items-center gap-1">
          <X className="h-3 w-3" />
          {status}
        </Badge>;
      case 'Suspendu':
        return <Badge className="status-badge-suspended px-3 py-1 text-xs font-semibold rounded-full border-0 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {status}
        </Badge>;
      default:
        return <Badge className="bg-gray-500 text-white px-3 py-1 text-xs font-semibold rounded-full border-0">{status}</Badge>;
    }
  };

  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const searchMatch = searchTerm === '' ||
        user.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase());
      const roleMatch = !filterRole || user.role === filterRole;
      const statusMatch = !filterStatus || user.statut === filterStatus;
      return searchMatch && roleMatch && statusMatch;
    });
  }, [users, searchTerm, filterRole, filterStatus]);

  const resetFilters = () => {
    setSearchTerm('');
    setFilterRole(null);
    setFilterStatus(null);
  };

  const getAvatarFallback = (name: string) => {
    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
    return initials.length > 1 ? initials.substring(0, 2) : initials;
  };

  const getAvatarRingClass = (role: string) => {
    switch (role) {
      case 'Administrateur': return 'ring-red-500';
      case 'Superviseur': return 'ring-yellow-500';
      case 'Opérateur': return 'ring-green-500';
      default: return 'ring-gray-400';
    }
  };

  if (loading && !users.length) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
        <p className="mt-4 text-lg text-gray-700">Chargement des utilisateurs...</p>
        <p className="text-sm text-gray-500">Veuillez patienter.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-red-50 text-red-800 p-4">
        <AlertCircle className="h-16 w-16" />
        <h2 className="mt-4 text-2xl font-bold">Erreur de chargement</h2>
        <p className="mt-2 text-center max-w-md">{error}</p>
        <Button onClick={fetchUsers} className="mt-6 bg-red-600 hover:bg-red-700">
          <RefreshCw className="mr-2 h-4 w-4" />
          Réessayer
        </Button>
      </div>
    );
  }

  return (
    <>
      {/* Header moderne avec gradient */}
      <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white p-8 rounded-t-2xl shadow-2xl page-transition">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          <div className="flex items-center space-x-4 animate-fade-in-up">
            <div className="bg-white/20 backdrop-blur-sm p-3 rounded-xl animate-pulse-gentle">
              <Users className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold mb-2">Gestion des Utilisateurs</h1>
              <p className="text-blue-100 text-lg">Administration complète des comptes utilisateurs</p>
              <div className="flex items-center mt-2 text-blue-200">
                <span className="text-sm">{filteredUsers.length} utilisateur{filteredUsers.length > 1 ? 's' : ''} trouvé{filteredUsers.length > 1 ? 's' : ''}</span>
              </div>
            </div>
          </div>
          <Button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-white text-blue-700 hover:bg-blue-50 font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 animate-slide-in-right action-button"
          >
            <PlusCircle className="mr-2 h-5 w-5" />
            Nouvel utilisateur
          </Button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="bg-white border-x-2 border-blue-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200 stat-card animate-fade-in-up" style={{animationDelay: '0.1s'}}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Utilisateurs actifs</p>
                <p className="text-2xl font-bold text-green-700">{users.filter(u => u.statut === 'Actif').length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 stat-card animate-fade-in-up" style={{animationDelay: '0.2s'}}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Administrateurs</p>
                <p className="text-2xl font-bold text-blue-700">{users.filter(u => u.role === 'Administrateur').length}</p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-gradient-to-r from-purple-50 to-violet-50 p-4 rounded-xl border border-purple-200 stat-card animate-fade-in-up" style={{animationDelay: '0.3s'}}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Superviseurs</p>
                <p className="text-2xl font-bold text-purple-700">{users.filter(u => u.role === 'Superviseur').length}</p>
              </div>
              <HardHat className="h-8 w-8 text-purple-500" />
            </div>
          </div>
          <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-4 rounded-xl border border-orange-200 stat-card animate-fade-in-up" style={{animationDelay: '0.4s'}}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Opérateurs</p>
                <p className="text-2xl font-bold text-orange-700">{users.filter(u => u.role === 'Opérateur').length}</p>
              </div>
              <User className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>

        {/* Filtres améliorés */}
        <div className="filter-section p-6 rounded-xl mb-6 animate-fade-in-up" style={{animationDelay: '0.5s'}}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2 relative">
              <Label htmlFor="search" className="text-sm font-medium text-gray-700 mb-2 block">Recherche</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Rechercher par nom ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="role-filter" className="text-sm font-medium text-gray-700 mb-2 block">Rôle</Label>
              <Select onValueChange={setFilterRole} value={filterRole || ''}>
                <SelectTrigger className="w-full h-11 border-gray-300 focus:border-blue-500 rounded-lg">
                  <SelectValue placeholder="Tous les rôles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Administrateur">Administrateur</SelectItem>
                  <SelectItem value="Superviseur">Superviseur</SelectItem>
                  <SelectItem value="Opérateur">Opérateur</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status-filter" className="text-sm font-medium text-gray-700 mb-2 block">Statut</Label>
              <Select onValueChange={setFilterStatus} value={filterStatus || ''}>
                <SelectTrigger className="w-full h-11 border-gray-300 focus:border-blue-500 rounded-lg">
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Actif">Actif</SelectItem>
                  <SelectItem value="Inactif">Inactif</SelectItem>
                  <SelectItem value="Suspendu">Suspendu</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex justify-end items-center gap-3 mt-4">
            <Button variant="ghost" onClick={resetFilters} className="text-gray-600 hover:text-blue-600 hover:bg-blue-50">
              <X className="mr-2 h-4 w-4" />
              Réinitialiser
            </Button>
            <Button variant="outline" onClick={fetchUsers} className="border-gray-300 hover:border-blue-500 hover:text-blue-600">
              <RefreshCw className="mr-2 h-4 w-4" />
              Actualiser
            </Button>
          </div>
        </div>

        {/* Tableau moderne avec cartes */}
        <div className="space-y-4">
          {filteredUsers.length > 0 ? (
            <div className="grid gap-4">
              {filteredUsers.map((user, index) => (
                <div key={user.id} className="bg-white border border-gray-200 rounded-xl p-6 user-card animate-fade-in-up" style={{animationDelay: `${0.6 + index * 0.1}s`}}>
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    {/* Informations utilisateur */}
                    <div className="flex items-center space-x-4 flex-1">
                      <div className="relative avatar-glow">
                        <Avatar className={`h-14 w-14 ring-3 ${getAvatarRingClass(user.role)} shadow-lg`}>
                          <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.nom}`} alt={user.nom} />
                          <AvatarFallback className="text-lg font-bold">{getAvatarFallback(user.nom)}</AvatarFallback>
                        </Avatar>
                        <span className={`absolute -bottom-1 -right-1 block h-4 w-4 rounded-full ${user.statut === 'Actif' ? 'bg-green-500 status-indicator active' : user.statut === 'Suspendu' ? 'bg-red-500' : 'bg-gray-400'} ring-2 ring-white shadow-sm`}></span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-1">
                          <h3 className="text-lg font-semibold text-gray-900 truncate">{user.nom}</h3>
                          {getRoleBadge(user.role)}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Mail className="h-4 w-4" />
                            <span className="truncate">{user.email}</span>
                          </div>
                          {user.telephone && (
                            <div className="flex items-center gap-1">
                              <Phone className="h-4 w-4" />
                              <span>{user.telephone}</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-4 mt-2">
                          {getStatusBadge(user.statut)}
                          <span className="text-xs text-gray-500">
                            Créé le {user.date_creation ? new Date(user.date_creation).toLocaleDateString('fr-FR') : 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 lg:flex-shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 border-blue-200 action-button"
                        onClick={() => { setSelectedUser(user); setIsDetailsModalOpen(true); }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Voir
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700 border-green-200 action-button"
                        onClick={() => { setSelectedUser(user); setIsEditModalOpen(true); }}
                      >
                        <FilePenLine className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className={`action-button ${
                          user.statut === 'Actif'
                            ? 'bg-yellow-50 text-yellow-600 hover:bg-yellow-100 hover:text-yellow-700 border-yellow-200'
                            : 'bg-emerald-50 text-emerald-600 hover:bg-emerald-100 hover:text-emerald-700 border-emerald-200'
                        }`}
                        onClick={() => handleToggleUserStatus(user)}
                      >
                        {user.statut === 'Actif' ? (
                          <>
                            <UserX className="h-4 w-4 mr-1" />
                            Désactiver
                          </>
                        ) : (
                          <>
                            <UserCheck className="h-4 w-4 mr-1" />
                            Activer
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 border-red-200 action-button"
                        onClick={() => handleDeleteUser(user.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Supprimer
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white border-2 border-dashed border-gray-300 rounded-xl p-12 text-center">
              <div className="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                <Users className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Aucun utilisateur trouvé</h3>
              <p className="text-gray-600 mb-6">Aucun utilisateur ne correspond à vos critères de recherche.</p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button variant="outline" onClick={resetFilters} className="border-gray-300 hover:border-blue-500 hover:text-blue-600">
                  <X className="mr-2 h-4 w-4" />
                  Réinitialiser les filtres
                </Button>
                <Button onClick={() => setIsAddModalOpen(true)} className="bg-blue-600 hover:bg-blue-700 text-white">
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Ajouter un utilisateur
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
        </CardContent>
        <CardFooter className="bg-gray-50 p-4 text-xs text-gray-500">
          <p>{filteredUsers.length} sur {users.length} utilisateurs affichés.</p>
        </CardFooter>
      </Card>

      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-lg bg-white shadow-2xl rounded-lg">
          <DialogHeader className="bg-blue-50 p-4 rounded-t-lg">
            <DialogTitle className="text-2xl font-bold text-gray-800 flex items-center">
              <UserCheck className="mr-3 text-blue-600" />
              Ajouter un nouvel utilisateur
            </DialogTitle>
            <DialogDescription className="text-gray-600">Remplissez les informations ci-dessous pour créer un profil.</DialogDescription>
          </DialogHeader>
          <div className="p-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="nom-add" className="text-sm font-medium text-gray-700">Nom complet</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-blue-400" />
                <Input id="nom-add" placeholder="Ex: Jean Dupont" value={newUser.nom} onChange={(e) => setNewUser({ ...newUser, nom: e.target.value })} className="pl-10 h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email-add" className="text-sm font-medium text-gray-700">Adresse e-mail</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-blue-400" />
                <Input id="email-add" type="email" placeholder="Ex: <EMAIL>" value={newUser.email} onChange={(e) => setNewUser({ ...newUser, email: e.target.value })} className="pl-10 h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="tel-add" className="text-sm font-medium text-gray-700">Téléphone (Optionnel)</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-blue-400" />
                <Input id="tel-add" placeholder="Ex: 06 12 34 56 78" value={newUser.telephone} onChange={(e) => setNewUser({ ...newUser, telephone: e.target.value })} className="pl-10 h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Rôle</Label>
                <Select value={newUser.role} onValueChange={(value) => setNewUser({ ...newUser, role: value })}>
                  <SelectTrigger className="w-full h-10 border-gray-300 focus:ring-blue-500 focus:ring-1">
                    <div className="flex items-center gap-2">
                      <Briefcase className="h-5 w-5 text-blue-400" />
                      <SelectValue placeholder="Choisir un rôle" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Opérateur">Opérateur</SelectItem>
                    <SelectItem value="Superviseur">Superviseur</SelectItem>
                    <SelectItem value="Administrateur">Administrateur</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Statut</Label>
                <Select value={newUser.statut} onValueChange={(value) => setNewUser({ ...newUser, statut: value })}>
                  <SelectTrigger className="w-full h-10 border-gray-300 focus:ring-blue-500 focus:ring-1">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-blue-400" />
                      <SelectValue placeholder="Choisir un statut" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Actif">Actif</SelectItem>
                    <SelectItem value="Inactif">Inactif</SelectItem>
                    <SelectItem value="Suspendu">Suspendu</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter className="bg-gray-50 p-4 rounded-b-lg">
            <Button variant="ghost" onClick={() => setIsAddModalOpen(false)} disabled={savingUser}>Annuler</Button>
            <Button onClick={handleAddUser} disabled={savingUser} className="bg-blue-600 hover:bg-blue-700 text-white font-bold shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5">
              {savingUser && <Loader2 className="mr-2 h-4 w-4 animate-spin" />} 
              {savingUser ? 'Ajout en cours...' : 'Créer l\'utilisateur'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <UserDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        user={selectedUser}
      />

      <UserEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        user={selectedUser}
        onSave={handleSaveUser}
      />
    </>
  );
}
